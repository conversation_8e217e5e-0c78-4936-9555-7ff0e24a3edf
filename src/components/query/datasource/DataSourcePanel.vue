<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { watch } from '@/plugins/vue-types-fix'
import { useDataSourceStore } from '@/stores/datasource'
// import { useSchemaStore } from '@/stores/schema'
// import { useDashboardStore } from '@/stores/dashboard'
import { message } from 'ant-design-vue'
import type { DataSource } from '@/types/datasource'
import type { SchemaMetadata } from '@/types/metadata'
import DataSourceSelector from '@/components/datasource/DataSourceSelector.vue'
import MetadataExplorer from '@/components/query/MetadataExplorer.vue'

// 组件属性定义
const props = defineProps<{
  dataSourceId?: string
  isPublic?: boolean
  selectedSchema?: string
}>()

// 事件定义
const emit = defineEmits<{
  (e: 'schemaChange', schema: string): void
  (e: 'dataSourceChange', dataSourceId: string): void
  (e: 'tableSelect', table: { name: string, [key: string]: any }): void
  (e: 'columnSelect', column: { name: string, [key: string]: any }, table: { name: string, [key: string]: any }): void
  (e: 'insertTable', tableName: string): void
  (e: 'insertColumn', columnName: string): void
}>()

// 状态管理
const dataSourceStore = useDataSourceStore()
// const schemaStore = useSchemaStore()
// const dashboardStore = useDashboardStore()
const metadataExplorerRef = ref<InstanceType<typeof MetadataExplorer> | null>(null)

// 数据源相关状态
const selectedDataSourceId = ref<string>('')
const selectedSchema = ref<string>('')
const schemaError = ref<string | null>(null)
const isLoadingSchemas = ref<boolean>(false)
const isRefreshingMetadata = ref<boolean>(false)

// 监听外部传入的selectedSchema变化
watch(() => props.selectedSchema, (newSchema) => {
  selectedSchema.value = newSchema || '';
}, { immediate: true });

// 监听内部selectedSchema变化，通知父组件
watch(() => selectedSchema.value, (newSchema) => {
  // 只有当schema不是来自外部props时才触发事件，避免循环
  if (newSchema && newSchema !== props.selectedSchema) {
    emit('schemaChange', newSchema);
  }
});

// 固定为元数据浏览面板
const leftPanel = ref<string>('metadata')

// 计算属性：获取当前选中的数据源
const selectedDataSource = computed(() => {
  if (!selectedDataSourceId.value) return null;



  // 1. 首先从dataSources列表中查找
  const dataSourceFromList = dataSourceStore.dataSources.find(
    (ds: any) => ds.id === selectedDataSourceId.value
  );

  if (dataSourceFromList) {
    return dataSourceFromList;
  }

  // 2. 如果列表中没有，尝试从currentDataSource获取
  if (dataSourceStore.currentDataSource && dataSourceStore.currentDataSource.id === selectedDataSourceId.value) {
    return dataSourceStore.currentDataSource;
  }

  // 3. 都没找到则返回null
  return null;
})

// 计算属性：判断当前数据源是否可用
const isDataSourceActive = computed(() => {
  const dataSource = selectedDataSource.value;
  // 确保selectedDataSource.value存在且状态为active
  return Boolean(dataSource && dataSource.status === 'active');
})

// 计算属性：获取处理后的Schema列表
const processedSchemas = computed(() => {
  if (!selectedDataSourceId.value) return []

  // 使用dataSourceStore.metadataState.schemas代替不存在的getSchemasByDataSourceId方法
  const schemas = dataSourceStore.metadataState?.schemas?.get(selectedDataSourceId.value) || []

  // 确保使用正确的ID字段作为value
  return schemas.map((schema: any) => {
    // 明确提取ID字段，优先使用schema.id作为value
    const schemaId = schema.id || schema.value;

    return {
      name: schema.name || schema.value || schema,
      // 优先使用schema.id，这是真正的唯一标识符
      value: schemaId || schema.value || schema.name || schema,
      id: schemaId,
      tablesCount: schema.tablesCount
    };
  });
})

// 处理数据源选择事件（接收完整的数据源对象）
const handleDataSourceSelected = async (dataSourceId: string, dataSource: any) => {
  try {
    // 更新选中的数据源ID并通知父组件
    selectedDataSourceId.value = dataSourceId;
    emit('dataSourceChange', dataSourceId);

    // 将选中的数据源添加到store中，确保后续查找能找到
    if (dataSourceId && dataSource) {
      try {
        // 检查store中是否已存在该数据源
        const existingIndex = dataSourceStore.dataSources.findIndex(ds => ds.id === dataSourceId);
        if (existingIndex !== -1) {
          // 更新已存在的数据源
          dataSourceStore.dataSources[existingIndex] = dataSource;
        } else {
          // 添加新数据源到store
          dataSourceStore.dataSources.push(dataSource);
        }
      } catch (error) {
        console.warn('添加数据源到store失败，但不影响后续操作:', error);
      }
    }

    // 只有在没有外部传入的selectedSchema时才清空schema选择
    if (!props.selectedSchema) {
      selectedSchema.value = '';
      emit('schemaChange', '');
    }
    
    schemaError.value = null;

    // 通知元数据浏览器清空表数据
    nextTick(async () => {
      await loadSchemas();
    });
  } catch (error) {
    console.error('处理数据源选择时出错:', error);
  }
};

// 处理数据源变更事件（仅ID变更）
const handleDataSourceChange = async (dataSourceId: string) => {
  try {

    // 更新选中的数据源ID并通知父组件
    selectedDataSourceId.value = dataSourceId;
    emit('dataSourceChange', dataSourceId);

    // 确保选中的数据源被加载到store中
    if (dataSourceId) {
      try {
        const result = await dataSourceStore.getDataSourceById(dataSourceId);
      } catch (error) {
      }
    }

    // 只有在没有外部传入的selectedSchema时才清空schema选择
    if (!props.selectedSchema) {
      selectedSchema.value = '';
      emit('schemaChange', '');
    }
    
    schemaError.value = null;

    // 通知元数据浏览器清空表数据
    nextTick(async () => {
      await loadSchemas();
    });
  } catch (error) {
    console.error('处理数据源变更时出错:', error);
  }
};

// 监听props.dataSourceId变化
watch(() => props.dataSourceId, (newVal: string | undefined) => {
  // 只有当newVal存在且与当前选中值不同时才处理
  if (newVal && newVal !== selectedDataSourceId.value) {
    // 通过调用handleDataSourceChange来确保统一的处理逻辑
    handleDataSourceChange(newVal);
  }
}, { immediate: true });

// 监听数据源ID变化来加载schemas
watch(() => selectedDataSourceId.value, (newVal: string, oldVal: string) => {
  // 如果新值为空，清空schemas
  if (!newVal) {
    return;
  }

  // 防止重复加载同一数据源
  if (newVal === oldVal) {
    return;
  }

  // 异步加载schemas
  loadSchemas();
});

// Schema被选中时
const handleSchemaSelected = async () => {
  // 先检查是否有选中的Schema和数据源
  if (!selectedSchema.value || !selectedDataSourceId.value) {
    return
  }

  // 添加当前选中的schema的详细信息
  const selectedSchemaObj = processedSchemas.value.find((s: any) => s.value === selectedSchema.value);

  // 触发schemaChange事件，通知父组件
  emit('schemaChange', selectedSchema.value)

  // 确保MetadataExplorer组件实例存在
  if (metadataExplorerRef.value) {
    // 显式调用加载表信息的方法，传递schemaId参数
    try {
      // 确保传递的是schemaId而非数据源ID
      await metadataExplorerRef.value.loadTables(selectedSchema.value)
    } catch (error) {
      console.error('加载表信息失败:', error)
      message.error('加载表信息失败')
    }
  } else {
    console.warn('MetadataExplorer组件实例不存在，无法加载表信息')
  }
}

// 加载schemas
const loadSchemas = async () => {
  if (!selectedDataSourceId.value) {
    return;
  }

  isLoadingSchemas.value = true;
  schemaError.value = null;

  let schemasLoadSuccess = false;

  try {
    // 尝试加载schemas
    await dataSourceStore.getSchemas(selectedDataSourceId.value);
    schemasLoadSuccess = true;

    // 添加防护检查，确保processedSchemas.value存在并且是数组
    const schemas = processedSchemas.value || [];

    if (schemas && schemas.length > 0) {
      // 只有在没有外部传入schema的情况下才清空，否则保持外部传入的值
      if (!props.selectedSchema) {
        selectedSchema.value = '';
      }
    } else if (schemasLoadSuccess && metadataExplorerRef.value) {
      // 如果没有schema但schemas加载成功，尝试无schema加载表
      metadataExplorerRef.value.loadTablesWithoutSchema(selectedDataSourceId.value);
    }
  } catch (error: any) {
    // Schema加载失败
    schemasLoadSuccess = false;
    console.error('加载schema失败:', error);
    schemaError.value = error.message || '加载schema失败';
    message.error('加载schema失败: ' + (error.message || '未知错误'));
    // Schema加载失败，不执行后续的loadTablesWithoutSchema操作
  } finally {
    isLoadingSchemas.value = false;
  }
}

// 刷新元数据
const refreshMetadata = async () => {
  if (!selectedDataSourceId.value) return

  isRefreshingMetadata.value = true
  schemaError.value = null

  try {
    // 清除缓存
    dataSourceStore.clearMetadataCache(selectedDataSourceId.value)

    // 重新加载schemas
    await dataSourceStore.getSchemas(selectedDataSourceId.value)

    // 如果当前已选择schema，重新加载tables
    if (selectedSchema.value) {
      await dataSourceStore.getTables(selectedDataSourceId.value, selectedSchema.value)
    }

    message.success('元数据已刷新')
  } catch (err: any) {
    console.error('刷新元数据失败:', err)
    schemaError.value = err instanceof Error ? err.message : '刷新元数据失败'
    message.error('刷新元数据失败')
  } finally {
    isRefreshingMetadata.value = false
  }
}

// 处理元数据浏览器中的表格选择
const handleTableSelect = (table: {name: string, [key: string]: any}) => {
  emit('tableSelect', table)
}

// 处理元数据浏览器中的列选择
const handleColumnSelect = (column: {name: string, [key: string]: any}, table: {name: string, [key: string]: any}) => {
  emit('columnSelect', column, table)
}

// 向编辑器插入表名
const insertTableName = (tableName: string) => {
  emit('insertTable', tableName)
}

// 向编辑器插入列名
const insertColumnName = (columnName: string) => {
  emit('insertColumn', columnName)
}

// 加载数据
onMounted(() => {
  if (props.dataSourceId) {
    handleDataSourceChange(props.dataSourceId)
  }
})
</script>

<template>
  <div class="bg-white shadow rounded-lg" :data-current-datasource-id="selectedDataSourceId">
    <!-- 数据源选择区域 -->
    <div class="p-4 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-medium text-gray-900">数据源</h3>
        <button
          @click="refreshMetadata"
          class="p-1 text-gray-500 hover:text-indigo-600 rounded"
          :class="{ 'animate-spin': isRefreshingMetadata }"
          :disabled="isRefreshingMetadata || !selectedDataSourceId"
          title="刷新元数据"
        >
          <i class="fas fa-sync-alt"></i>
        </button>
      </div>

      <!-- 使用DataSourceSelector组件 -->
      <div class="mt-2">
        <DataSourceSelector
          v-model="selectedDataSourceId"
          :is-public="isPublic"
          module-key="QUERY"
          placeholder="请选择数据源"
          @selected="handleDataSourceSelected"
        />
      </div>

      <!-- Schema选择器 -->
      <div class="mt-2" v-if="isDataSourceActive">
        <div class="relative">
          <select
            v-model="selectedSchema"
            class="appearance-none bg-white block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            :disabled="isLoadingSchemas"
            @change="handleSchemaSelected"
          >
            <option value="">{{ isLoadingSchemas ? '加载Schema中...' : '请选择Schema' }}</option>
            <option
              v-for="schema in processedSchemas"
              :key="schema.value"
              :value="schema.value"
            >
              {{ schema.name }}
            </option>
          </select>
          <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
            <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
              <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </div>
        </div>

        <!-- 加载状态和错误提示 -->
        <div v-if="schemaError" class="mt-2 text-sm text-red-600">
          <i class="fas fa-exclamation-circle mr-1"></i>
          {{ schemaError }}
        </div>
      </div>
    </div>

    <!-- 元数据浏览面板 -->
    <div v-if="leftPanel === 'metadata'" class="h-[calc(100vh-24rem)] overflow-y-auto">
      <!-- 判断是否选择了数据源 -->
      <div v-if="selectedDataSourceId">
        <!-- 判断所选数据源是否可用 -->
        <div v-if="selectedDataSource && selectedDataSource.status === 'active'">
          <!-- 这里是关键修改：无论是否选择了Schema都显示MetadataExplorer -->
          <MetadataExplorer
            ref="metadataExplorerRef"
            :dataSourceId="selectedDataSourceId"
            :schema="selectedSchema"
            :selectedSchema="selectedSchema"
            @table-select="handleTableSelect"
            @column-select="handleColumnSelect"
            @insert-table="insertTableName"
            @insert-column="insertColumnName"
            @noSchema="(hasNoSchema: boolean) => { console.log('数据源无Schema:', hasNoSchema) }"
          />
        </div>
        <!-- 数据源不可用时显示提示 -->
        <div v-else class="p-4 text-center">
          <div class="text-yellow-600 mb-2">
            <i class="fas fa-exclamation-triangle mr-2"></i>
            选中的数据源当前不可用
          </div>
          <p class="text-gray-500 text-sm">
            该数据源状态为: {{ selectedDataSource ? selectedDataSource.status : '未知' }}
            <br>
            请选择一个可用的数据源或联系管理员解决问题
          </p>
        </div>
      </div>
      <!-- 未选择数据源时显示提示 -->
      <div v-else class="p-4 text-center text-gray-500">
        请先选择一个数据源
      </div>
    </div>
  </div>
</template>
