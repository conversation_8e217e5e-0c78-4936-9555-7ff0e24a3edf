<template>
  <nav class="bg-white shadow-lg">
    <div class="max-w-7xl mx-auto px-4">
      <div class="flex justify-between h-16">
        <div class="flex">
          <div class="flex-shrink-0 flex items-center">
            <img alt="DataScope" class="h-14 w-auto" src="@/assets/images/logo.png"/>
          </div>
          <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
            <!-- 内部路由使用router-link -->
            <router-link
              v-for="item in internalNavItems"
              :key="item.path"
              :to="item.path"
              :class="[
                'inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium',
                isActiveRoute(item.path)
                  ? 'border-indigo-500 text-gray-900'
                  : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
              ]"
            >
              {{ item.name }}
            </router-link>

            <!-- 外部链接使用a标签 -->
            <a
              v-for="item in externalNavItems"
              :key="item.path"
              :href="item.path"
              class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"
            >
              {{ item.name }}
            </a>
          </div>
        </div>

        <!-- 用户信息和退出登录区域 -->
        <div class="flex items-center">
          <a-dropdown v-if="userStore.isAuthenticated" :trigger="['click']">
            <div class="user-avatar-container flex items-center cursor-pointer">
              <a-avatar v-if="userStore.currentUser?.avatar" :src="userStore.currentUser.avatar" />
              <a-avatar v-else>{{ userAvatarText }}</a-avatar>
              <span class="ml-2 text-sm font-medium text-gray-700">{{ userStore.currentUser?.name || userStore.currentUser?.username }}</span>
              <down-outlined class="ml-1" />
            </div>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="handleLogout">
                  <logout-outlined />
                  <span class="ml-2">退出登录</span>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>

          <a-button v-else type="primary" size="small" @click="goToLogin">
            登录
          </a-button>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { DownOutlined, LogoutOutlined } from '@ant-design/icons-vue'
import { authConfig } from '@/utils/config'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
// const baseUrl = import.meta.env.BASE_URL || '/'
const baseUrl = '/'

// 内部导航和外部导航分开定义
const internalNavItems = [
  { name: '首页', path: baseUrl === '/' ? '/' : baseUrl },
  { name: '数据源', path: `${baseUrl}datasource/`.replace('//', '/') },
  { name: '查询服务', path: `${baseUrl}query`.replace('//', '/') },
  { name: '操作服务', path: `${baseUrl}operation`.replace('//', '/') },
  { name: '系统集成', path: `${baseUrl}integration`.replace('//', '/') }
  // { name: '系统页面', path: `${baseUrl}pages`.replace('//', '/') }
]

// 外部导航项
const externalNavItems: { name: string; path: string }[] = [
  // 可以根据需要添加外部导航项
]

// 判断导航项是否处于激活状态
const isActiveRoute = (path: string) => {
  // 处理根路径特殊情况
  if (path === '/' || path === baseUrl) {
    return route.path === '/' || route.path === baseUrl
  }

  // 考虑baseUrl的情况下检查路径是否匹配
  const routePath = route.path.startsWith(baseUrl) ? route.path.slice(baseUrl.length - 1) : route.path
  const navPath = path.startsWith(baseUrl) ? path.slice(baseUrl.length - 1) : path

  return routePath.startsWith(navPath)
}

// 用户头像显示文本（取用户名首字母）
const userAvatarText = computed(() => {
  const username = userStore.currentUser?.name || userStore.currentUser?.username || ''
  return username.substring(0, 1).toUpperCase()
})

// 检查用户登录状态
const checkUserLogin = () => {
  // 检查验证状态与用户信息是否存在
}

// 组件挂载时检查状态
onMounted(() => {
  // 如果有token但用户状态为空，尝试获取用户信息
  const storedToken = localStorage.getItem('token')
  if (storedToken && (!userStore.user || !userStore.isAuthenticated)) {
    userStore.setToken(storedToken)
    setTimeout(() => {
      userStore.fetchUserProfile()
    }, 200)
  }
})

// 监听用户状态变化
watch(() => userStore.token, () => {
  // token变化时的处理逻辑
})

// 退出登录处理
const handleLogout = async () => {
  try {
    // 调用退出登录API
    await userStore.logout()

    // 使用配置中的登录URL
    const loginUrl = authConfig.loginUrl

    // 清除其他浏览器存储的数据
    sessionStorage.clear()

    // 不再将当前页面的URL作为回调
    // 而是使用默认的首页或系统指定页面作为登录后的目标页面
    const defaultPage = `${window.location.origin}${window.location.pathname.split('/')[0]}`

    // 跳转到登录页面，添加回调页面，但添加logout参数确保页面加载时能清除本地状态
    // 使用首页作为回调，而不是当前页面
    const homepageUrl = `${window.location.origin}${window.location.pathname.split('/')[0]}?logout=true`
    window.location.href = `${loginUrl}?callback=${encodeURIComponent(homepageUrl)}`
  } catch (error) {
    console.error('退出登录失败:', error)
  }
}

// 前往登录页
const goToLogin = () => {
  // 使用配置中的登录URL
  const loginUrl = authConfig.loginUrl

  // 使用当前页面的完整URL作为回调
  const callbackUrl = window.location.href

  // 跳转到登录页面
  window.location.href = `${loginUrl}?callback=${encodeURIComponent(callbackUrl)}`
}
</script>

<style scoped>
.user-avatar-container {
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-avatar-container:hover {
  background-color: rgba(0, 0, 0, 0.05);
}
</style>