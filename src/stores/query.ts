import {defineStore} from 'pinia'
import {computed, ref} from 'vue'
import type {
    ChartConfig,
    Query,
    QueryDisplayConfig,
    QueryExecutionPlan,
    QueryFavorite,
    QueryHistoryParams,
    QueryResult,
    QueryServiceStatus,
    QueryStatus,
    QuerySuggestion,
    QueryType,
    QueryVisualization,
    SaveQueryParams
} from '@/types/query'
import type {BaseQueryParams, PaginationInfo} from '@/types/common'
import {queryService} from '@/services/query'
import {queryStatusService} from '@/services/queryStatus'
import {useMessageService} from '@/services/message'
import {loading} from '@/services/loading'
import {getErrorMessage} from '@/utils/error'
import {useRoute} from 'vue-router'
import instance from "@/utils/axios";
import { globalApiRequestManager } from '@/utils/requestManager';

// 查询参数接口
interface QueryParams extends BaseQueryParams {
  dataSourceId?: string
  status?: QueryStatus
  serviceStatus?: string
  queryType?: QueryType
  includeDrafts?: boolean
  searchTerm?: string
}

// 查询API类型定义
export interface QueryAPI {
  getQueries(): Promise<Query[]>
  getQuery(id: string): Promise<Query | null>
  saveQuery(query: Query): Promise<Query>
  favoriteQuery(id: string): Promise<boolean>
  unfavoriteQuery(id: string): Promise<boolean>
  executeQuery(query: Query): Promise<QueryResult>
  getFavorites(): Promise<Query[]>
  getQueryHistory(): Promise<Query[]>
  getQueryVisualization(id: string): Promise<QueryVisualization | null>
  getQueryExecutionPlan(id: string): Promise<QueryExecutionPlan | null>
  getQuerySuggestions(id: string): Promise<QuerySuggestion[]>
  exportQueryResults(id: string, format: 'csv' | 'excel' | 'json'): Promise<boolean>
  // 新增方法：获取查询执行历史
  getQueryExecutionHistory(id: string): Promise<QueryExecution[]>
  // 新增方法：获取特定执行记录的结果
  getExecutionResults(executionId: string): Promise<QueryResult | null>
  // 新增方法：获取执行错误信息
  getExecutionError(executionId: string): Promise<QueryExecutionError | null>
}

// 查询执行记录类型
export interface QueryExecution {
  id: string                    // 执行ID
  queryId: string               // 关联的查询ID
  executedAt: string            // 执行时间
  executionTime: number         // 执行耗时（毫秒）
  status: QueryStatus           // 执行状态
  rowCount?: number             // 结果行数（如果成功）
  errorMessage?: string         // 错误信息（如果失败）
}

// 查询执行错误信息
export interface QueryExecutionError {
  executionId: string           // 执行ID
  errorCode: string             // 错误代码
  errorMessage: string          // 错误消息
  errorDetails?: string         // 详细错误信息
  stackTrace?: string           // 堆栈跟踪
}

export const useQueryStore = defineStore('query', () => {
  // 获取消息服务
  const messageService = useMessageService()
  const route = useRoute()

  // 状态
  const queries = ref<Query[]>([])
  const currentQuery = ref<Query | null>(null)
  const currentQueryResult = ref<QueryResult | null>(null)
  const queryHistory = ref<Query[]>([])
  const favorites = ref<QueryFavorite[]>([])
  const displayConfig = ref<QueryDisplayConfig | null>(null)
  const executionPlan = ref<QueryExecutionPlan | null>(null)
  const suggestions = ref<QuerySuggestion[]>([])
  const visualization = ref<QueryVisualization | null>(null)
  const executionHistory = ref<QueryExecution[]>([])
  const pagination = ref<PaginationInfo>({
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0,
    hasMore: false
  })
  const isExecuting = ref(false)
  const isLoading = ref(false)
  const error = ref<Error | null>(null)

  // 计算属性
  const hasResult = computed(() => !!currentQueryResult.value && currentQueryResult.value.rows.length > 0)

  const completedQueries = computed(() => {
    return queryHistory.value.filter(q => q.status === 'COMPLETED')
  })

  const favoriteQueries = computed(() => {
    return queryHistory.value.filter(q => q.isFavorite)
  })

  /**
   * 执行SQL查询
   * @param params 查询参数
   * @returns 查询结果
   */
  const executeQuery = async (params: {
    dataSourceId: string;
    queryText?: string;
    sql?: string;
    queryType: string;
    versionId?: string; // 添加可选的版本ID参数
    parameters?: Record<string, any>;
    isNewQuery?: boolean; // 添加标志，指示是否是新增查询
    page?: number; // 分页页码
    size?: number; // 每页条数
    schemaId?: string; // 新增：schema ID参数
  }): Promise<QueryResult> => {
    console.log("[Store:Query] 准备执行查询:", params)
    isExecuting.value = true
    error.value = null

    try {
      // 检查参数
      if (!params.dataSourceId) {
        throw new Error('数据源ID不能为空')
      }

      // 确保有查询文本，优先使用sql字段，兼容旧的queryText字段
      const sqlText = params.sql || params.queryText
      if (!sqlText || !sqlText.trim()) {
        throw new Error('查询语句不能为空')
      }

      // 打印详细日志，便于排查参数问题
      console.log("[Store:Query] 执行查询，详细参数:", {
        dataSourceId: params.dataSourceId,
        sqlText,
        queryLength: sqlText.length,
        queryType: params.queryType,
        versionId: params.versionId || '(未指定)',
          hasParameters: params.parameters ? Object.keys(params.parameters).length : 0,
          isNewQuery: params.isNewQuery || false
      })

      // 从API执行查询
      // 只有在同时满足以下条件时才使用版本接口执行特定查询：
      // 1. 有当前查询ID
      // 2. 有版本ID参数
      // 3. 版本ID不是'default'
        // 4. 不是新增查询
        if (currentQuery.value?.id && params.versionId && params.versionId !== 'default' && !params.isNewQuery) {
        console.log(`[Store:Query] 使用API执行特定查询版本: 查询ID=${currentQuery.value.id}, 版本ID=${params.versionId}`)
        const response = await queryService.executeQueryVersion(
          currentQuery.value.id,
          params.versionId,
          {
            parameters: params.parameters || {}
          }
        )

        // 保存当前查询结果，添加类型转换
        currentQueryResult.value = response as unknown as QueryResult

        return response as unknown as QueryResult
      }

      // 否则执行普通查询（使用execute-sql接口）
      console.log(`[Store:Query] 使用API执行普通查询: 查询类型=${params.queryType}`)

      // 构建API执行参数，正确传递参数
      const apiParams = {
        dataSourceId: params.dataSourceId,
        // 【重要】使用sql作为主要参数，这是后端API需要的
        sql: sqlText,
        // 保留queryType参数
        queryType: params.queryType || 'SQL',
        // 传递参数
        parameters: params.parameters || {},
        // 添加分页参数
        page: params.page,
        size: params.size,
        // 添加schemaId参数
        schemaId: params.schemaId
      };

      // 如果提供了版本ID，添加到参数中
      if (params.versionId) {
        // 使用类型断言来添加versionId，解决TypeScript检查问题
        (apiParams as any).versionId = params.versionId;
        console.log("[Store:Query] 添加版本ID到执行参数:", params.versionId);
      }

      // 记录完整请求参数便于排查
      console.log("[Store:Query] 最终发送的请求参数:", JSON.stringify(apiParams, null, 2));
      console.log("[Store:Query] 关键参数检查:");
      console.log("- sql参数存在:", !!apiParams.sql);
      console.log("- sql参数长度:", apiParams.sql ? apiParams.sql.length : 0);
      console.log("- 数据源ID:", apiParams.dataSourceId);
      console.log("- 分页参数:", {
        page: apiParams.page,
        size: apiParams.size,
        '是否包含分页': !!(apiParams.page !== undefined && apiParams.size !== undefined)
      });

      const response = await queryService.executeQuery(apiParams);

      // 保存当前查询结果，添加类型转换
      currentQueryResult.value = response as unknown as QueryResult

      return response as unknown as QueryResult
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : String(err)
      error.value = new Error(`执行查询失败: ${errorMsg}`)
      console.error('[Store:Query] 执行查询失败:', error.value)
      throw error.value
    } finally {
      isExecuting.value = false
    }
  }

  /**
   * 执行自然语言查询
   * @param params 查询参数
   * @returns 查询结果
   */
  const executeNaturalLanguageQuery = async (params: {
    dataSourceId: string;
    question: string;
    versionId?: string; // 添加可选的版本ID参数
    parameters?: Record<string, any>;
  }): Promise<QueryResult> => {
    console.log("[Store:Query] 准备执行自然语言查询:", params)
    isExecuting.value = true
    error.value = null

    try {
      // 检查参数
      if (!params.dataSourceId) {
        throw new Error('数据源ID不能为空')
      }

      if (!params.question || !params.question.trim()) {
        throw new Error('问题不能为空')
      }

      // 构建API执行参数 - 调整为使用与后端API期望匹配的参数名
      const apiParams: any = {
        question: params.question,
        parameters: params.parameters || {}
      };

      // 如果提供了有效的版本ID且不是'default'，添加到参数中
      if (params.versionId && params.versionId !== 'default') {
        apiParams.version_id = params.versionId;
        console.log("[Store:Query] 添加版本ID到自然语言查询参数:", params.versionId);
      }

      // 传递dataSourceId作为URL查询参数而非请求体的一部分
      const response = await queryService.executeNaturalLanguageQuery({
        dataSourceId: params.dataSourceId,
        question: params.question,
        parameters: params.parameters || {}
      })

      // 保存当前查询结果
      currentQueryResult.value = response

      return response
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : String(err)
      error.value = new Error(`执行自然语言查询失败: ${errorMsg}`)
      console.error('[Store:Query] 执行自然语言查询失败:', error.value)
      throw error.value
    } finally {
      isExecuting.value = false
    }
  }

  // 取消查询
  const cancelQuery = async (queryId: string) => {
    try {
      loading.show('取消查询中...')

      await queryService.cancelQuery(queryId)

      // 更新当前查询状态
      if (currentQuery.value && currentQuery.value.id === queryId) {
        currentQuery.value.status = 'CANCELLED'
      }

      // 更新历史中的查询状态
      const historyQuery = queryHistory.value.find(q => q.id === queryId)
      if (historyQuery) {
        historyQuery.status = 'CANCELLED'
      }

      // 强制设置执行状态为未执行
      isExecuting.value = false

      // 更新结果状态（如果有结果）
      if (currentQueryResult.value && currentQueryResult.value.id === queryId) {
        currentQueryResult.value.status = 'CANCELLED'
      }

      messageService.success('查询已取消')
      return true
    } catch (err) {
      console.error('取消查询错误:', err)
      error.value = err instanceof Error ? err : new Error(String(err))
      messageService.error('取消查询失败')

      // 即使API调用失败，也要强制停止执行状态
      isExecuting.value = false

      return false
    } finally {
      loading.hide()
    }
  }

  // 获取查询状态
  const getQueryStatus = async (queryId: string) => {
    try {
      const status = await queryService.getQueryStatus(queryId)

      // 更新当前查询状态
      if (currentQuery.value && currentQuery.value.id === queryId) {
        currentQuery.value.status = status.status

        if (status.result && status.status === 'COMPLETED') {
          currentQueryResult.value = status.result
        }
      }

      return status
    } catch (err) {
      console.error('获取查询状态失败', err)
      return null
    }
  }

  // 获取查询历史
  const fetchQueryHistory = async (params: QueryHistoryParams = { page: 1, size: 10 }) => {
    try {
      console.log('[Store:Query] 开始获取查询历史, 参数:', params);
      loading.show('获取查询历史...')

      // 确保params中至少有page和size参数
      const safeParams = {
        page: params.page || 1,
        size: params.size || 10,
        queryType: params.queryType,
        status: params.status,
        startDate: params.startDate,
        endDate: params.endDate,
        searchTerm: params.searchTerm
      };

      console.log('[Store:Query] 处理后的安全参数:', safeParams);

      // 调用API获取查询历史
      const result = await queryService.getQueryHistory(safeParams);

      console.log('[Store:Query] API返回原始结果:', result);

      // 将查询历史数据存储到store
      const historyItems = result.items || [];

      console.log('[Store:Query] 转换前的历史记录数:', historyItems.length);

      // 将ExecutionHistory类型转换为Query类型
      const convertedItems: Query[] = historyItems.map(item => ({
        id: item.id,
        name: `执行历史 ${item.id}`,
        description: '',
        status: item.status === 'SUCCESS' ? 'COMPLETED' :
                (item.status === 'ERROR' ? 'FAILED' : 'CANCELLED'),
        executionTime: (item as any).duration || 0,
        resultCount: (item as any).rowCount || 0,
        queryId: item.queryId,
        createdAt: item.executedAt,
        updatedAt: item.executedAt,
        isFavorite: false,
        executionCount: 1,
        lastExecutedAt: item.executedAt
      }));

      console.log('[Store:Query] 转换后的查询记录:', {
        记录数: convertedItems.length,
        第一条: convertedItems[0],
        最后一条: convertedItems[convertedItems.length - 1]
      });

      queryHistory.value = convertedItems;

      // 更新分页信息 - 直接计算总页数，不依赖API返回值
      const total = result.total || 0;
      const calculatedTotalPages = Math.ceil(total / safeParams.size);

      pagination.value = {
        page: safeParams.page,
        pageSize: safeParams.size,
        total: total,
        totalPages: calculatedTotalPages,
        hasMore: safeParams.page < calculatedTotalPages
      };

      console.log('[Store:Query] 更新后的分页信息:', {
        当前页: pagination.value.page,
        每页条数: pagination.value.pageSize,
        总记录数: pagination.value.total,
        计算得到的总页数: pagination.value.totalPages,
        是否可以下一页: pagination.value.hasMore
      });

      return result;
    } catch (err) {
      console.error('[Store:Query] 获取查询历史失败:', err);
      error.value = err instanceof Error ? err : new Error(String(err));
      messageService.error('获取查询历史失败');
      throw error.value;
    } finally {
      loading.hide();
    }
  }

  // 获取查询详情
  const getQuery = async (id: string) => {
    if (!id) {
      error.value = new Error('查询ID不能为空')
      return null
    }

    try {
      loading.show('加载查询信息...')
      currentQuery.value = await queryService.getQuery(id)

      if (!currentQuery.value) {
        error.value = new Error(`未找到ID为 ${id} 的查询，该查询可能已被删除或不存在`)
        return null
      }

      return currentQuery.value
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(String(err))
      messageService.error(error.value.message)
      return null
    } finally {
      loading.hide()
    }
  }

  // 保存查询
  const saveQuery = async (params: SaveQueryParams) => {
    try {
      loading.show('保存查询中...')

      const savedQuery = await queryService.saveQuery(params)

      // 如果当前查询是被保存的查询，更新当前查询
      if (currentQuery.value && (params.id === currentQuery.value.id || !params.id)) {
        currentQuery.value = savedQuery
      }

      messageService.success('查询保存成功')

      // 检查查询历史中是否存在该ID
      const existsInHistory = queryHistory.value.some(q => q.id === savedQuery.id)
      if (!existsInHistory) {
        console.log('保存的查询ID不在历史记录中，但不再重新加载查询历史')
      }

      // 同时更新保存的查询列表(queries)
      const existsInQueries = queries.value.some(q => q.id === savedQuery.id)
      if (!existsInQueries) {
        // 如果是新查询，添加到查询列表的开头
        queries.value = [savedQuery, ...queries.value]
      } else {
        // 如果是更新查询，替换已有的
        queries.value = queries.value.map(q =>
          q.id === savedQuery.id ? savedQuery : q
        )
      }

      return savedQuery
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(String(err))
      messageService.error('保存查询失败')
      throw error.value
    } finally {
      loading.hide()
    }
  }

  // 删除查询
  const deleteQuery = async (id: string): Promise<boolean> => {
    try {
      // 开始加载
      isExecuting.value = true;

      // 调用接口删除
      await queryService.deleteQuery(id);

      // 从数据中移除
      queries.value = queries.value.filter(query => query.id !== id);

      // 显示成功消息
      messageService.success('查询已成功删除');

      return true;
    } catch (error) {
      console.error('删除查询失败:', error);
      const errorMessage = getErrorMessage(error);
      messageService.error(`删除查询失败: ${errorMessage}`);
      return false;
    } finally {
      isExecuting.value = false;
    }
  }

  // 创建新查询
  const createQuery = async (params: SaveQueryParams) => {
    console.log('创建新查询:', params);
    // 创建查询实际上是调用saveQuery，但不传id
    const queryParams = { ...params };
    delete queryParams.id; // 确保不传id，强制创建新查询
    return await saveQuery(queryParams);
  }

  // 删除查询历史
  const deleteQueryHistory = async (historyId: string) => {
    try {
      loading.show('删除查询历史中...')

      // queryService可能没有提供deleteQueryHistory方法，使用通用的删除方法
      await instance.delete(`/api/queries/history/${historyId}`)

      // 从历史中移除
      queryHistory.value = queryHistory.value.filter(q => q.id !== historyId)

      messageService.success('查询历史已删除')
      return true
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(String(err))
      messageService.error('删除查询历史失败')
      return false
    } finally {
      loading.hide()
    }
  }

  // 收藏查询
  const favoriteQuery = async (id: string) => {
    try {
      // 修改为正确的调用方式
      await queryService.favoriteQuery(id)

      // 更新本地状态
      const query = queryHistory.value.find(q => q.id === id)
      if (query) {
        query.isFavorite = true
      }

      messageService.success('已添加到收藏夹')
      return true
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(String(err))
      messageService.error('收藏查询失败')
      return false
    }
  }

  // 取消收藏
  const unfavoriteQuery = async (id: string) => {
    try {
      // 修改为正确的调用方式
      await queryService.unfavoriteQuery(id)

      // 更新本地状态
      const query = queryHistory.value.find(q => q.id === id)
      if (query) {
        query.isFavorite = false
      }

      // 如果是在收藏夹页面，同时从列表中移除
      if (route.path.includes('favorites')) {
        favorites.value = favorites.value.filter(f => f.queryId !== id)
      }

      messageService.success('已从收藏夹移除')
      return true
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(String(err))
      messageService.error('取消收藏失败')
      return false
    }
  }

  // 加载收藏列表
  const getFavorites = async () => {
    try {
      const result = await queryService.getFavoriteQueries()

      // 将PageResponse<Query>转换为QueryFavorite[]
      const favoriteItems: QueryFavorite[] = (result.items || []).map(query => ({
        id: `fav-${query.id}`,
        queryId: query.id,
        userId: 'current-user',
        name: query.name,
        description: query.description,
        createdAt: query.createdAt,
        updatedAt: query.updatedAt,
        query: query
      }))

      favorites.value = favoriteItems
      return favoriteItems
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(String(err))
      console.error('加载收藏夹失败', error.value)
      return []
    }
  }

  // 保存显示配置
  const saveDisplayConfig = async (queryId: string, config: Partial<QueryDisplayConfig>) => {
    try {
      loading.show('保存显示配置...')

      // 直接使用http调用API
      const result = await instance.post<QueryDisplayConfig>(`/api/queries/${queryId}/display-config`, config)
      displayConfig.value = result.data

      messageService.success('显示配置已保存')
      return result.data
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(String(err))
      messageService.error('保存显示配置失败')
      return null
    } finally {
      loading.hide()
    }
  }

  // 加载显示配置
  const getDisplayConfig = async (queryId: string) => {
    try {
      // 直接使用http调用API
      const result = await instance.get<QueryDisplayConfig>(`/api/queries/${queryId}/display-config`)
      displayConfig.value = result.data
      return result.data
    } catch (err) {
      // 如果没有显示配置，不显示错误
      console.log('No display config found for query', queryId)
      displayConfig.value = null
      return null
    }
  }

  // 获取查询优化建议
  const getQuerySuggestions = async (queryId: string) => {
    try {
      // 直接使用http调用API
      const result = await instance.get<QuerySuggestion[]>(`/api/queries/${queryId}/suggestions`)
      suggestions.value = result.data
      return result.data
    } catch (err) {
      console.error('获取查询优化建议失败', err)
      return []
    }
  }

    // 获取查询执行计划 - 基于ID的方式
    const getQueryExecutionPlan = async (id: string) => {
        try {
            console.log(`开始获取执行计划，ID: ${id}`)

            // 检查是否是新增查询页面或临时ID
            const isNewQuery = window.location.pathname.includes('/query/new') ||
                window.location.pathname.includes('/query/create') ||
                !id ||
                id.includes('temp-');

            console.log(`getQueryExecutionPlan: 检测是否为新增查询: ${isNewQuery}`);

            // 如果是新增查询，优先尝试使用SQL方式获取
            if (isNewQuery && currentQuery.value?.queryText && currentQuery.value?.dataSourceId) {
                console.log('检测到新增查询，直接使用SQL方式获取执行计划:', {
                    sql: currentQuery.value.queryText,
                    dataSourceId: currentQuery.value.dataSourceId
                });

                return await getExecutionPlanForSql({
                    dataSourceId: currentQuery.value.dataSourceId,
                    sql: currentQuery.value.queryText,
                    parameters: {}
                });
            }

            // 使用查询ID获取执行计划
            const apiUrl = `/api/queries/${id}/execution-plan`;

            try {
                // 直接使用http调用API
                const result = await instance.get<QueryExecutionPlan>(apiUrl)

                if (result.data) {
                    console.log('成功获取执行计划:', result.data)
                    executionPlan.value = result.data
                    return result.data
                } else {
                    console.log('执行计划暂不可用')
                }
            } catch (idError) {
                // 如果通过ID获取失败，检查是否有当前查询和数据源ID
                console.log('通过ID获取执行计划失败，尝试使用SQL方式获取', idError)

                // 获取当前查询
                const currentQueryData = currentQuery.value

                // 如果有当前查询，并且有SQL和数据源ID，尝试使用SQL方式获取
                if (currentQueryData && currentQueryData.queryText && currentQueryData.dataSourceId) {
                    console.log('尝试使用SQL方式获取执行计划:', {
                        sql: currentQueryData.queryText,
                        dataSourceId: currentQueryData.dataSourceId
                    })

                    // 调用基于SQL的方法获取执行计划
                    return await getExecutionPlanForSql({
                        dataSourceId: currentQueryData.dataSourceId,
                        sql: currentQueryData.queryText,
                        parameters: {}
                    })
                } else {
                    // 如果没有足够的信息，抛出原始错误
                    throw idError
                }
            }

            return null
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : String(err)
            console.error('获取执行计划错误:', err)

      // 不显示错误通知，因为这是后台功能，不应该影响用户体验
            throw new Error(`获取执行计划失败: ${errorMsg}`)
        }
    }

    // 获取未保存查询的执行计划 - 基于SQL的方式
    const getExecutionPlanForSql = async (params: {
        dataSourceId: string;
        sql: string;
        parameters?: Record<string, any>;
    }) => {
        try {
            console.log('开始获取未保存查询的执行计划')

            if (!params.dataSourceId) {
                throw new Error('数据源ID不能为空')
            }

            if (!params.sql || !params.sql.trim()) {
                throw new Error('SQL语句不能为空')
            }

            // 使用专门的执行计划接口
            const apiUrl = '/api/queries/execution-plan';

            // 准备请求参数
            const requestData = {
                dataSourceId: params.dataSourceId,
                sql: params.sql,
                parameters: params.parameters || {}
            };

            console.log('发送执行计划请求:', requestData);

            // 发送POST请求
            const result = await instance.post<QueryExecutionPlan>(apiUrl, requestData);

            if (result.data) {
                console.log('成功获取执行计划:', result.data)
                executionPlan.value = result.data
            } else {
                console.log('执行计划暂不可用')
            }

            return result.data
        } catch (err) {
            const errorMsg = err instanceof Error ? err.message : String(err)
            console.error('获取执行计划错误:', err)

            throw new Error(`获取执行计划失败: ${errorMsg}`)
    }
  }

  // 获取查询可视化
  const getQueryVisualization = async (queryId: string) => {
    try {
      console.log('获取查询可视化, ID:', queryId)
      loading.show('加载查询可视化...')

      // 直接使用http调用API
      const result = await instance.get<QueryVisualization[]>(`/api/queries/${queryId}/visualizations`)
      const visualizationData = Array.isArray(result.data) ? result.data[0] : result.data

      if (visualizationData) {
        visualization.value = visualizationData
        return visualizationData.config
      }
      return null
    } catch (err) {
      console.error('获取查询可视化失败', err)
      return null
    }
  }

  // 保存查询可视化
  const saveQueryVisualization = async (queryId: string, config: ChartConfig) => {
    try {
      // 构建一个符合QueryVisualization类型的对象
      const visualizationItem: QueryVisualization = {
        id: `vis-${queryId}`,
        queryId,
        name: '查询可视化',
        type: config.type,
        config,
        createdAt: new Date().toISOString()
      }

      // 直接使用http调用API
      const result = await instance.post<QueryVisualization>(`/api/queries/${queryId}/visualizations`, [visualizationItem])

      // 更新本地状态
      visualization.value = Array.isArray(result.data) ? result.data[0] : result.data
      return visualization.value
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(String(err))
      console.error('保存查询可视化失败', error.value)
      throw error.value
    }
  }

  // 导出查询结果
  const exportQueryResults = async (queryId: string, format: 'csv' | 'excel' | 'json') => {
    try {
      loading.show(`正在导出为 ${format.toUpperCase()} 格式...`)

      // 使用instance.get替代fetch，并设置responseType为blob
      const url = `/api/queries/${queryId}/export?format=${format}`;
      const response = await instance.get(url, {
        responseType: 'blob'
      });

      if (!response || response.status !== 200) {
        throw new Error(`导出失败: ${response?.statusText || '未知错误'}`);
      }

      // 处理响应，下载文件
      const blob = new Blob([response.data]);
      const downloadUrl = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = downloadUrl;
      a.download = `query-${queryId}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(downloadUrl);
      document.body.removeChild(a);

      messageService.success(`查询结果已导出为 ${format.toUpperCase()} 格式`)
      return true
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(String(err))
      messageService.error(`导出为 ${format.toUpperCase()} 格式失败`)
      return false
    } finally {
      loading.hide()
    }
  }

  // 重置状态
  const resetState = () => {
    currentQuery.value = null
    currentQueryResult.value = null
    displayConfig.value = null
    executionPlan.value = null
    suggestions.value = []
    visualization.value = null
    error.value = null
  }

  // 初始化
  const init = async () => {
    await Promise.all([
      fetchQueryHistory(),
      getFavorites()
    ])
  }

  // 获取查询执行历史
  const getQueryExecutionHistory = async (queryId: string) => {
    try {
      loading.show('获取执行历史记录...')

      const history = await queryService.getQueryExecutionHistory(queryId)

      console.log(`获取到${history.length}条执行历史记录`)

      // 转换ExecutionHistory到QueryExecution
      const convertedHistory: QueryExecution[] = history.map(item => ({
        id: item.id,
        queryId: item.queryId,
        executedAt: item.executedAt,
        executionTime: (item as any).duration || 0,
        status: item.status === 'SUCCESS' ? 'COMPLETED' as QueryStatus :
               (item.status === 'ERROR' ? 'FAILED' as QueryStatus :
               'CANCELLED' as QueryStatus),
        rowCount: (item as any).rowCount,
        errorMessage: (item as any).errorMessage
      }))

      executionHistory.value = convertedHistory
      return convertedHistory
    } catch (err) {
      console.error('获取执行历史失败:', err)
      error.value = err instanceof Error ? err : new Error(String(err))
      messageService.error(`获取执行历史失败: ${getErrorMessage(err)}`)
      return []
    } finally {
      loading.hide()
    }
  }

  // 手动设置当前查询文本
  const setCurrentQueryText = (queryText: string) => {
    if (currentQuery.value) {
      currentQuery.value.queryText = queryText
    }
  }

  // 获取查询列表
  const fetchQueries = async (params?: QueryParams): Promise<Query[]> => {
    try {
      isLoading.value = true
      error.value = null

      console.log(`[Store:Query] 开始查询列表请求，参数:`, params)

      // 统一使用searchTerm参数，后端API支持searchTerm
      const queryParams = {
        page: params?.page || pagination.value.page,
        size: params?.size || pagination.value.pageSize,
        searchTerm: params?.search || params?.searchTerm || '',
        queryType: params?.queryType,
        status: params?.status,
        serviceStatus: params?.serviceStatus,
        dataSourceId: params?.dataSourceId,
        includeDrafts: params?.includeDrafts
      }

      console.log(`[Store:Query] 处理后的查询参数:`, queryParams)

      // 使用queryService获取查询列表
      const response = await queryService.getQueries(queryParams)

      console.log(`[Store:Query] 查询列表原始响应:`, response)

      // 处理响应数据
      let items: Query[] = []
      let paginationInfo = { ...pagination.value }

      // 统一处理标准响应格式 {success: true, data: {items: [], total: 0, page: 1, ...}}
      if (response && response.success === true && response.data) {
        // 检查data.items是否存在并且是数组
        if (response.data.items && Array.isArray(response.data.items)) {
          items = response.data.items

          // 标准化查询对象中的serviceStatus字段，确保大写
          items.forEach(query => {
            if (query.serviceStatus) {
              query.serviceStatus = query.serviceStatus.toUpperCase()
            }
          })

          // 计算总页数
          const total = response.data.total || 0
          const pageSize = response.data.size || queryParams.size
          const calculatedTotalPages = Math.ceil(total / pageSize)

          // 更新分页信息
          paginationInfo = {
            total: total,
            page: response.data.page || queryParams.page,
            pageSize: pageSize,
            totalPages: calculatedTotalPages,
            hasMore: (response.data.page || queryParams.page) < calculatedTotalPages
          }

          console.log(`[Store:Query] 分页计算结果:`, {
            总记录数: total,
            每页大小: pageSize,
            计算得到的总页数: calculatedTotalPages,
            当前页: paginationInfo.page,
            是否可以下一页: paginationInfo.hasMore
          })

          console.log(`[Store:Query] 成功解析查询列表: ${items.length}条记录`)
        }
        // 兼容性处理：如果data本身是数组（旧API可能的返回格式）
        else if (Array.isArray(response.data)) {
          items = response.data
          console.log(`[Store:Query] 兼容格式处理，从data数组获取 ${items.length} 条记录`)
        }
        else {
          console.error('[Store:Query] 响应中没有找到有效的查询数据:', response.data)
        }
      } else {
        console.error('[Store:Query] 无效的响应格式:', response)
        throw new Error('获取查询列表失败：无效的响应格式')
      }

      // 更新store状态
      console.log(`[Store:Query] 更新后的分页状态:`, paginationInfo)

      queries.value = items
      pagination.value = paginationInfo

      return items
    } catch (e) {
      const errorMsg = getErrorMessage(e)
      console.error(`[Store:Query] 获取查询列表失败:`, errorMsg)
      error.value = new Error(`获取查询列表失败: ${errorMsg}`)
      messageService.error(`获取查询列表失败: ${errorMsg}`)
      return []
    } finally {
      isLoading.value = false
    }
  }

  // 更新查询状态
  const updateQueryStatus = async (id: string, serviceStatus: QueryServiceStatus) => {
    try {
      loading.show('更新查询状态...')

      let result;
      // 根据目标状态选择调用不同的API
      if (serviceStatus === 'ENABLED') {
        // 调用启用API
        result = await queryStatusService.enableQuery(id);
      } else {
        // 调用禁用API - 需要传递正确格式的参数
        result = await queryStatusService.disableQuery({
          queryId: id,
          reason: '手动禁用查询' // 提供一个默认原因
        });
      }

      console.log('查询状态更新API返回结果:', result);

      // 更新本地状态 - 支持多种成功响应格式
      // 检查返回的是否是标准的状态对象，或带有status字段的对象
      const isSuccess = result && (
        // 检查状态匹配预期状态 - 标准对象响应
        ('status' in result &&
         (result.status === serviceStatus ||
          result.status === 'ENABLED' ||
          result.status === 'DISABLED')) ||
        // 支持简单的成功响应结构 {"success":true,"data":true}
        ('success' in result && result.success === true) ||
        // 支持布尔值响应
        result === true
      );

      if (isSuccess) {
        // 更新查询列表中的状态
        const index = queries.value.findIndex(q => q.id === id)
        if (index !== -1) {
          queries.value[index] = {
            ...queries.value[index],
            serviceStatus: serviceStatus
          }
        }

        // 如果当前查询是被更新的查询，也更新当前查询
        if (currentQuery.value && currentQuery.value.id === id) {
          currentQuery.value.serviceStatus = serviceStatus
        }
      } else if (result && typeof result === 'object' && 'status' in result && result.status === 'ERROR') {
        // 明确的错误状态 - 使用类型断言来处理自定义错误对象
        const errorResult = result as { status: string; error?: string };
        throw new Error(errorResult.error || `${serviceStatus === 'ENABLED' ? '启用' : '禁用'}查询失败: 服务返回错误状态`);
      } else {
        // 其他无法识别的响应格式 - 尝试提取可能的错误信息
        console.error('无法识别的响应格式:', result);

        let errorMsg = `${serviceStatus === 'ENABLED' ? '启用' : '禁用'}查询失败: 服务返回无效响应`;

        // 尝试从不同的错误格式中提取错误信息
        if (result && typeof result === 'object') {
          if ('message' in result && result.message) {
            errorMsg = `${serviceStatus === 'ENABLED' ? '启用' : '禁用'}查询失败: ${result.message}`;
          } else if ('error' in result && result.error) {
            const errorText = typeof result.error === 'string' ? result.error : JSON.stringify(result.error);
            errorMsg = `${serviceStatus === 'ENABLED' ? '启用' : '禁用'}查询失败: ${errorText}`;
          } else if ('code' in result && result.code !== 200) {
            errorMsg = `${serviceStatus === 'ENABLED' ? '启用' : '禁用'}查询失败 (错误码: ${result.code})`;
          }
        }

        throw new Error(errorMsg);
      }

      return result
    } catch (err) {
      // 增强错误日志，记录完整错误信息
      console.error(`${serviceStatus === 'ENABLED' ? '启用' : '禁用'}查询失败:`, err);

      // 详细记录错误对象
      if (err instanceof Error) {
        console.error('错误类型:', err.constructor.name);
        console.error('错误消息:', err.message);
        console.error('错误堆栈:', err.stack);
      } else {
        console.error('非标准错误对象:', err);
      }

      // 设置更有用的错误消息
      error.value = err instanceof Error ? err : new Error(String(err));

      // 确保错误消息不为空
      const errorMessage = error.value.message || `${serviceStatus === 'ENABLED' ? '启用' : '禁用'}查询失败`;
      messageService.error(`${serviceStatus === 'ENABLED' ? '启用' : '禁用'}查询失败: ${errorMessage}`);

      // 返回一个包含错误信息的对象而不是null，这样调用者可以正确处理错误
      return {
        status: 'ERROR' as const,
        error: errorMessage
      };
    } finally {
      loading.hide()
    }
  }

  /**
   * 获取查询参数列表
   * @param queryId - 查询ID
   * @returns 查询参数列表
   */
  const getQueryParameters = async (queryId: string): Promise<any[]> => {
    try {
      return await queryService.getQueryParameters(queryId);
    } catch (error) {
      console.error('获取查询参数失败:', error);
      messageService.error(`获取查询参数失败: ${error instanceof Error ? error.message : String(error)}`);
      return [];
    }
  };

  /**
   * 获取查询字段列表
   * @param queryId 查询ID
   * @returns 字段列表
   */
  const getQueryFields = async (queryId: string): Promise<string[]> => {
    try {
      loading.show();
      console.log(`[Store:Query] 获取查询字段, 查询ID: ${queryId}`);

      // 使用parameters API获取字段信息
      const definitionUrl = `/api/queries/${queryId}/parameters`;
      console.log(`[Store:Query] 请求查询定义: ${definitionUrl}`);

      const response = await globalApiRequestManager.request(
        () => instance.get(definitionUrl),
        definitionUrl,
        { queryId },
        { cacheTime: 30000 }
      );
      const data = response.data;

      // 从查询定义数据中提取字段
      if (data.success && data.data) {
        // 检查fields是否存在于QueryDefinitionDTO中
        if (data.data.fields && Array.isArray(data.data.fields)) {
          console.log(`[Store:Query] 从QueryDefinitionDTO中找到${data.data.fields.length}个字段`);
          return data.data.fields.map((field: any) =>
            typeof field === 'string' ? field : field.name || field.field
          );
        }
      }

      // 如果未找到字段信息，尝试从parameters提取字段
      if (data.success && data.data && data.data.parameters) {
        // 推测：尝试从参数中提取可能的字段信息
        console.log(`[Store:Query] 从参数中推断字段信息`);
        const potentialFields = new Set<string>();

        // 检查参数名可能是字段名
        data.data.parameters.forEach((param: any) => {
          if (param.name) {
            potentialFields.add(param.name);
          }
        });

        if (potentialFields.size > 0) {
          const fieldsArray = Array.from(potentialFields);
          console.log(`[Store:Query] 从参数名推断出${fieldsArray.length}个可能的字段`);
          return fieldsArray;
        }
      }

      console.warn('[Store:Query] 无法获取字段信息，返回空数组');
      return [];
    } catch (error) {
      console.error('[Store:Query] 获取查询字段出错:', error);
      return [];
    } finally {
      loading.hide();
    }
  }

  // 获取查询定义文件
  const getQueryDefinition = async (queryId: string): Promise<string> => {
    try {
      error.value = null;
      const definitionUrl = `/api/queries/${queryId}/definition?_t=${Date.now()}`;

      const response = await instance.get(definitionUrl, {
        responseType: 'text'
      });

      if (!response || response.status !== 200) {
        throw new Error(`获取查询定义失败: ${response?.statusText || '未知错误'}`);
      }

      return response.data;
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(String(err));
      messageService.error(`获取查询定义失败: ${getErrorMessage(err)}`);
      throw error.value;
    }
  }

  // 更新查询数据源ID
  const updateQueryDataSource = async (queryId: string, dataSourceId: string) => {
    try {
      console.log(`[查询存储] 更新查询数据源ID: 查询ID=${queryId}, 新数据源ID=${dataSourceId}`);

      // 如果当前查询是正在编辑的查询，直接更新本地状态
      if (currentQuery.value && currentQuery.value.id === queryId) {
        console.log(`[查询存储] 为当前编辑的查询更新数据源ID`);
        currentQuery.value.dataSourceId = dataSourceId;
      }

        // 在查询列表中寻找并更新
      const index = queries.value.findIndex(q => q.id === queryId);
      if (index !== -1) {
        console.log(`[查询存储] 在查询列表中找到查询，索引为 ${index}`);
        queries.value[index].dataSourceId = dataSourceId;
      }

        // 不调用API立即保存，而是在下次保存时一并提交
      console.log(`[查询存储] 数据源ID更新成功，将在下次保存时提交`);
      return true;
    } catch (err) {
      console.error(`[查询存储] 更新数据源ID失败:`, err);
      return false;
    }
  };

  return {
    // 状态
    queries,
    currentQuery,
    currentQueryResult,
    queryHistory,
    favorites,
    displayConfig,
    executionPlan,
    suggestions,
    visualization,
    executionHistory,
    pagination,
    isExecuting,
    isLoading,
    error,

    // 计算属性
    hasResult,
    completedQueries,
    favoriteQueries,

    // 方法
    executeQuery,
    executeNaturalLanguageQuery,
    cancelQuery,
    getQueryStatus,
    fetchQueryHistory,
    getQuery,
    saveQuery,
    deleteQuery,
    favoriteQuery,
    unfavoriteQuery,
    getFavorites,
    saveDisplayConfig,
    getDisplayConfig,
    getQuerySuggestions,
    getQueryExecutionPlan,
      getExecutionPlanForSql,
    getQueryVisualization,
    saveQueryVisualization,
    exportQueryResults,
    resetState,
    init,
    getQueryExecutionHistory,
    setCurrentQueryText,
    fetchQueries,
    deleteQueryHistory,
    updateQueryStatus,
    getQueryParameters,
    createQuery,
    getQueryFields,
    getQueryDefinition,
    updateQueryDataSource
  }
})

export default useQueryStore
