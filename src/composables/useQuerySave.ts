import { ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useQueryStore } from '@/stores/query';
import { message } from 'ant-design-vue';
import axios from 'axios';
import type { QuerySaveData } from '@/types/queryEditor';
import type { SaveQueryParams } from '@/types/query';

/**
 * 查询保存相关逻辑的组合式API
 */
export function useQuerySave() {
  // 路由
  const route = useRoute();
  const router = useRouter();
  
  // Store
  const queryStore = useQueryStore();
  
  // 状态
  const isSaveModalVisible = ref(false);
  const statusMessage = ref<string | null>(null);
  const currentQueryId = ref<string | null>(null);
  const queryName = ref('');
  
  // 检查组件状态工具
  let isComponentMounted = true;
  
  /**
   * 打开保存查询对话框
   * @param selectedDataSourceId 当前选择的数据源ID
   * @param queryText 当前查询文本
   */
  const openSaveModal = (selectedDataSourceId: string, queryText: string | null) => {
    // 检查组件是否已卸载
    if (!isComponentMounted) return;
    
    // 在显示保存对话框前，验证必要数据
    if (!selectedDataSourceId) {
      statusMessage.value = '请先选择数据源再保存查询';
      setTimeout(() => {
        if (!isComponentMounted) return;
        statusMessage.value = null;
      }, 3000);
      return;
    }
    
    // 验证查询内容
    if (!queryText || queryText.trim() === '') {
      statusMessage.value = '查询内容不能为空';
      setTimeout(() => {
        if (!isComponentMounted) return;
        statusMessage.value = null;
      }, 3000);
      return;
    }
    
    // 显示保存对话框
    isSaveModalVisible.value = true;
  };
  
  /**
   * 处理保存查询
   * @param saveData 保存的数据
   * @param selectedDataSourceId 选中的数据源ID
   * @param activeTab 当前活动的标签页
   * @param sqlQuery SQL查询文本
   * @param naturalLanguageQuery 自然语言查询文本
   */
  const handleSaveQuery = async (
    saveData: QuerySaveData, 
    selectedDataSourceId: string,
    activeTab: 'editor' | 'nlq' | 'builder',
    sqlQuery: string,
    naturalLanguageQuery: string
  ) => {
    try {
      isSaveModalVisible.value = false;
      statusMessage.value = '正在保存查询...';
      
      console.log('准备保存的数据:', saveData);
      console.log('当前选择的数据源:', selectedDataSourceId);
      console.log('当前活动标签页:', activeTab);
      
      // 获取当前查询文本
      const queryText = activeTab === 'editor' ? sqlQuery : naturalLanguageQuery;
      
      // 验证必填字段
      if (!selectedDataSourceId) {
        throw new Error('请选择数据源');
      }
      if (!queryText || queryText.trim() === '') {
        throw new Error('查询内容不能为空');
      }
      
      // 构造符合接口要求的对象
      const queryData: SaveQueryParams = {
        name: saveData.name,
        dataSourceId: selectedDataSourceId,
        sql: queryText,
        description: saveData.description || ''
      };
      
      // 如果是更新场景，添加id
      const queryId = route.query.id;
      if (queryId && typeof queryId === 'string') {
        queryData.id = queryId;
      }
      
      console.log('最终保存查询对象:', queryData);
      
      // 调用保存查询接口
      const result = await queryStore.saveQuery(queryData);
      
      if (result && result.id) {
        message.success('查询保存成功');
        statusMessage.value = '查询保存成功';
        
        // 更新当前查询ID
        currentQueryId.value = result.id;
        
        // 更新查询名称
        queryName.value = saveData.name || '未命名查询';
        
        // 如果是新建查询，更新URL并添加到历史记录
        if (!route.query.id) {
          const newQuery = { ...route.query, id: result.id };
          router.replace({ query: newQuery });
        }
        
        // 创建查询草稿版本（但不发布或激活）
        try {
          statusMessage.value = '正在创建查询草稿版本...';
          
          // 调用版本API
          const versionApi = axios.create({
            baseURL: '', // 设置为空，让拦截器处理
            timeout: 10000,
            headers: {
              'Content-Type': 'application/json'
            }
          });
          
          // 检查是否使用模拟数据
          const USE_MOCK = import.meta.env.VITE_USE_MOCK_API === 'true';
          
          // 确定请求的URL
          const apiUrl = USE_MOCK 
            ? `/api/queries/${result.id}/versions` 
            : `${import.meta.env.VITE_API_BASE_URL || ''}/api/queries/${result.id}/versions`;
          
          console.log(`创建查询草稿版本:`, apiUrl, '模拟模式:', USE_MOCK ? '是' : '否');
          
          // 添加请求拦截器，以便于调试
          versionApi.interceptors.request.use((config) => {
            console.log('版本API请求配置:', config);
            return config;
          });
          
          const versionResponse = await versionApi.post(apiUrl, {
            sqlContent: queryText,
            dataSourceId: selectedDataSourceId,
            description: saveData.description || ''
          });
          
          if (versionResponse.data && versionResponse.data.success) {
            console.log('草稿版本创建成功:', versionResponse.data);
            statusMessage.value = '查询保存成功，并创建了草稿版本';
          } else {
            console.warn('创建草稿版本响应数据异常:', versionResponse.data);
            statusMessage.value = '查询保存成功，但草稿版本创建失败';
          }
        } catch (versionError) {
          console.error('创建草稿版本失败:', versionError);
          // 版本创建失败不影响主流程，只记录日志
          statusMessage.value = '查询保存成功，但创建草稿版本失败';
        }
      
        setTimeout(() => {
          statusMessage.value = null;
        }, 3000);
      } else {
        throw new Error('保存查询失败');
      }
    } catch (error) {
      console.error('保存查询失败:', error);
      message.error(error instanceof Error ? error.message : '保存查询失败');
      statusMessage.value = error instanceof Error ? error.message : '保存查询失败';
      setTimeout(() => {
        statusMessage.value = null;
      }, 5000);
    }
  };
  
  /**
   * 保存查询草稿
   * @param queryText 查询文本
   * @param type 查询类型
   * @param queryId 查询ID
   */
  const saveDraft = async (queryText: string, type: 'SQL' | 'natural-language', queryId: string | null) => {
    try {
      statusMessage.value = '正在保存草稿...';
      
      // 验证是否有内容
      if (!queryText.trim()) {
        statusMessage.value = '无内容可保存';
        setTimeout(() => {
          statusMessage.value = null;
        }, 3000);
        return;
      }
      
      // 更新最后保存草稿时间
      const now = new Date().toISOString();
      
      // 根据是否有查询ID决定操作
      if (queryId) {
        // 已有查询，更新草稿
        // 构造保存草稿的请求数据
        const draftData = {
          id: queryId,
          queryText: queryText,
          queryType: type
        };
        
        // 这里可以调用API保存草稿
        // TODO: 替换为实际的API调用
        await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 500));
        
        message.success('草稿已保存');
        statusMessage.value = '草稿已保存';
      } else {
        // 新查询，首次保存草稿，静默存储到本地
        localStorage.setItem('query_draft_text', queryText);
        localStorage.setItem('query_draft_type', type);
        localStorage.setItem('query_draft_timestamp', now);
        
        message.success('草稿已临时保存');
        statusMessage.value = '草稿已临时保存';
      }
      
      setTimeout(() => {
        statusMessage.value = null;
      }, 3000);
      
      return now;
    } catch (error) {
      console.error('保存草稿失败:', error);
      message.error('保存草稿失败');
      statusMessage.value = '保存草稿失败';
      setTimeout(() => {
        statusMessage.value = null;
      }, 5000);
      return null;
    }
  };
  
  return {
    // 状态
    isSaveModalVisible,
    statusMessage,
    currentQueryId,
    queryName,
    
    // 方法
    openSaveModal,
    handleSaveQuery,
    saveDraft,
    
    // 标记组件卸载
    setComponentUnmounted: () => {
      isComponentMounted = false;
    }
  };
}